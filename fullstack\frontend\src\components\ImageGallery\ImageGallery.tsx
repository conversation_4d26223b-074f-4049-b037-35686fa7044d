import React, { useState } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi2";

interface ImageGalleryProps {
  className?: string;
}

interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  placeholder?: boolean;
}

function ImageGallery({ className = "" }: ImageGalleryProps) {
  // Sample placeholder charts for now
  const [galleryImages] = useState<GalleryImage[]>([
    { id: "1", src: "", alt: "Chart 1", placeholder: true },
    { id: "2", src: "", alt: "Chart 2", placeholder: true },
    { id: "3", src: "", alt: "Chart 3", placeholder: true },
    { id: "4", src: "", alt: "Chart 4", placeholder: true },
    { id: "5", src: "", alt: "Chart 5", placeholder: true },
    { id: "6", src: "", alt: "Chart 6", placeholder: true },
    { id: "7", src: "", alt: "Chart 7", placeholder: true },
    { id: "8", src: "", alt: "Chart 8", placeholder: true },
  ]);

  const [droppedImages, setDroppedImages] = useState<GalleryImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const visibleImages = 4; // Number of charts visible at once
  const maxIndex = Math.max(0, galleryImages.length - visibleImages);

  const handlePrevious = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => Math.min(maxIndex, prev + 1));
  };

  const handleDragStart = (e: React.DragEvent, image: GalleryImage) => {
    e.dataTransfer.setData("application/json", JSON.stringify(image));
    e.dataTransfer.effectAllowed = "copy";
    // Text selection prevention is handled by CSS (select-none class and userSelect: "none")
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      const imageData = JSON.parse(e.dataTransfer.getData("application/json"));

      // Only handle new items from gallery, not rearrangement
      // This allows dropping new charts anywhere in the drop container
      if (
        imageData &&
        !imageData.isRearrangement &&
        !droppedImages.find((img) => img.id === imageData.id)
      ) {
        setDroppedImages((prev) => [...prev, imageData]);
      }
    } catch (error) {
      console.error("Error parsing dropped data:", error);
    }
  };

  const removeDroppedImage = (imageId: string) => {
    setDroppedImages((prev) => prev.filter((img) => img.id !== imageId));
  };

  // Handle drag start for rearrangement within drop container
  const handleRearrangeDragStart = (e: React.DragEvent, index: number) => {
    console.log("🔄 Rearrangement drag start - index:", index);
    setDraggedItemIndex(index);
    e.dataTransfer.setData(
      "application/json",
      JSON.stringify({
        ...droppedImages[index],
        isRearrangement: true,
        originalIndex: index,
      })
    );
    e.dataTransfer.effectAllowed = "move";
    // Text selection prevention is handled by CSS (select-none class and userSelect: "none")
  };

  // Handle drag over for rearrangement
  const handleRearrangeDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();

    // Only show rearrangement feedback when we're dragging an existing chart
    // (draggedItemIndex is set during rearrangement drag start)
    if (draggedItemIndex !== null) {
      e.dataTransfer.dropEffect = "move";
      setDragOverIndex(index);
    }
  };

  // Handle drag leave for rearrangement
  const handleRearrangeDragLeave = () => {
    setDragOverIndex(null);
  };

  // Handle drop for rearrangement
  const handleRearrangeDrop = (e: React.DragEvent, targetIndex: number) => {
    console.log(
      "🎯 Rearrangement drop triggered - targetIndex:",
      targetIndex,
      "draggedItemIndex:",
      draggedItemIndex
    );
    e.preventDefault();

    try {
      const dragData = JSON.parse(e.dataTransfer.getData("application/json"));
      console.log("📦 Drag data:", dragData);

      // Only handle rearrangement drops, not new items from gallery
      if (dragData.isRearrangement && draggedItemIndex !== null) {
        console.log(
          "✅ Performing swap - from index",
          draggedItemIndex,
          "to index",
          targetIndex
        );
        // Stop propagation only when actually handling rearrangement
        // This allows gallery drops to bubble up to the container handler
        e.stopPropagation();

        // Swap the dragged item with the target item
        const newDroppedImages = [...droppedImages];

        // Simple swap: exchange positions of the two items
        const temp = newDroppedImages[draggedItemIndex];
        newDroppedImages[draggedItemIndex] = newDroppedImages[targetIndex];
        newDroppedImages[targetIndex] = temp;

        console.log(
          "🔄 Swap completed - new order:",
          newDroppedImages.map((img) => img.alt)
        );
        setDroppedImages(newDroppedImages);
      } else {
        console.log("❌ Not a rearrangement or draggedItemIndex is null");
      }
      // If not a rearrangement, let the event bubble up to container handler
    } catch (error) {
      console.error("Error handling rearrangement drop:", error);
    } finally {
      setDraggedItemIndex(null);
      setDragOverIndex(null);
    }
  };

  // Helper function to determine grid layout based on number of charts
  const getGridLayout = (imageCount: number) => {
    switch (imageCount) {
      case 1:
        return "grid-cols-1"; // 1x1 grid
      case 2:
        return "grid-cols-2"; // 2x1 grid
      case 3:
        return "grid-cols-3"; // 3x1 grid
      default:
        return "grid-cols-3"; // 3xN grid for 4+ images
    }
  };

  return (
    <div className={`bg-[#051844] py-8 sm:py-12 ${className}`}>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6">
        {/* Drop Container */}
        <div
          className={`mb-8 p-6 border-2 border-dashed rounded-lg transition-all duration-300 min-h-[200px] ${
            isDragOver
              ? "border-blue-400 bg-blue-400/10"
              : "border-gray-600 bg-[#233e6c]/30"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col">
            <h3 className="text-white text-xl font-bold mb-4 text-center">
              Drag Charts Here
            </h3>
            {droppedImages.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <p className="text-gray-400 text-sm text-center">
                  Drag charts from the gallery below to add them to your
                  collection
                </p>
              </div>
            ) : (
              <div className="w-full">
                <div
                  className={`grid ${getGridLayout(
                    droppedImages.length
                  )} gap-4 auto-rows-fr`}
                >
                  {droppedImages.map((image, index) => (
                    <div
                      key={image.id}
                      className={`relative group rounded-lg p-4 flex items-center justify-center cursor-move transition-all duration-200 select-none ${
                        dragOverIndex === index && draggedItemIndex !== index
                          ? "bg-blue-500/30 border-2 border-blue-400 border-dashed"
                          : draggedItemIndex === index
                          ? "bg-[#233e6c]/50 opacity-50"
                          : "bg-[#233e6c] hover:bg-[#2a4a7c]"
                      }`}
                      style={{
                        aspectRatio:
                          droppedImages.length === 1 ? "16 / 9" : "1 / 1",
                        minHeight:
                          droppedImages.length === 1 ? "200px" : "150px",
                        userSelect: "none",
                      }}
                      draggable
                      onDragStart={(e) => handleRearrangeDragStart(e, index)}
                      onDragOver={(e) => handleRearrangeDragOver(e, index)}
                      onDragLeave={handleRearrangeDragLeave}
                      onDrop={(e) => handleRearrangeDrop(e, index)}
                      onDragEnd={() => {
                        // Don't reset draggedItemIndex here - let the drop handler handle it
                        // This prevents race conditions where dragEnd fires before drop
                        console.log("🏁 Drag end triggered");
                      }}
                    >
                      {image.placeholder ? (
                        <div className="text-white text-center">
                          <div className="text-2xl mb-2">📊</div>
                          <div className="text-xs">{image.alt}</div>
                        </div>
                      ) : (
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-contain rounded"
                        />
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeDroppedImage(image.id);
                        }}
                        className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer hover:scale-110"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Chart Gallery */}
        <div className="bg-[#233e6c] rounded-xl p-6">
          <h3 className="text-white text-2xl font-bold mb-6 text-center">
            Chart Gallery
          </h3>

          <div className="relative">
            {/* Navigation Arrows */}
            <button
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              className={`absolute left-0 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full shadow-lg transition-all duration-300 ${
                currentIndex === 0
                  ? "bg-gray-700 text-gray-500 cursor-not-allowed opacity-50"
                  : "bg-[#051844] hover:bg-[#0a1a52] text-white hover:scale-105 cursor-pointer"
              }`}
              title="Previous charts"
            >
              <HiChevronLeft className="w-6 h-6" />
            </button>

            <button
              onClick={handleNext}
              disabled={currentIndex >= maxIndex}
              className={`absolute right-0 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full shadow-lg transition-all duration-300 ${
                currentIndex >= maxIndex
                  ? "bg-gray-700 text-gray-500 cursor-not-allowed opacity-50"
                  : "bg-[#051844] hover:bg-[#0a1a52] text-white hover:scale-105 cursor-pointer"
              }`}
              title="Next charts"
            >
              <HiChevronRight className="w-6 h-6" />
            </button>

            {/* Gallery Container */}
            <div className="overflow-hidden mx-12">
              <div
                className="flex gap-4 transition-transform duration-300 ease-in-out"
                style={{
                  transform: `translateX(-${
                    currentIndex * (100 / visibleImages)
                  }%)`,
                }}
              >
                {galleryImages.map((image) => (
                  <div
                    key={image.id}
                    className="flex-shrink-0 bg-[#051844] rounded-lg p-4 aspect-square cursor-grab active:cursor-grabbing hover:scale-105 transition-transform duration-200 select-none"
                    style={{
                      width: `calc(${100 / visibleImages}% - 0.75rem)`,
                      userSelect: "none",
                    }}
                    draggable
                    onDragStart={(e) => handleDragStart(e, image)}
                  >
                    <div className="w-full h-full flex items-center justify-center text-white text-center">
                      {image.placeholder ? (
                        <>
                          <div>
                            <div className="text-4xl mb-2">📊</div>
                            <div className="text-sm">{image.alt}</div>
                          </div>
                        </>
                      ) : (
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover rounded"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Gallery Indicators */}
          <div className="flex justify-center mt-6 gap-2">
            {Array.from({ length: maxIndex + 1 }, (_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 cursor-pointer ${
                  index === currentIndex
                    ? "bg-white"
                    : "bg-white/30 hover:bg-white/50"
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ImageGallery;
